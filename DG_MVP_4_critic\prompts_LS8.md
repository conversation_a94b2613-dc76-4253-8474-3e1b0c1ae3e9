## Prompt [LS8_1]

### Context
[`reflection_LS7.md:15-25`](reflection_LS7.md:15-25) (Issue 1) identified that the `calculate_dsm_intersections_vectorized` function in [`vectorized_georef.py:156-295`](vectorized_georef.py:156-295) is not fully vectorized. It still contains a per-ray loop ([`vectorized_georef.py:201`](vectorized_georef.py:201)), which hinders performance for DSM processing. The goal of LS7_2 ([`prompts_LS7.md:53-56`](prompts_LS7.md:53-56)) was full vectorization.

### Objective
Refactor the `calculate_dsm_intersections_vectorized` function in [`vectorized_georef.py`](vectorized_georef.py) to eliminate the explicit per-ray loop and perform DSM intersection calculations vectorially across all rays simultaneously.

### Focus Areas
- Generate all coarse sample points for *all* rays in a single vectorized operation.
- Perform bounds checking for all sample points vectorially.
- Query the DSM interpolator with all valid sample point coordinates in a batch (adapting interaction if necessary).
- Vectorize the sign change detection and bisection refinement steps across all relevant rays.

### Code Reference
[`vectorized_georef.py:156-295`](vectorized_georef.py:156-295)

### Requirements
- Eliminate the `for ray_idx in range(num_rays):` loop currently at [`vectorized_georef.py:201`](vectorized_georef.py:201).
- The refactored function must maintain or improve accuracy compared to the previous (partially vectorized) version.
